// React hooks are available globally via the script in index.html
// CryptoJS is expected to be available as a global variable.
// The library will be loaded dynamically within the component.

// Define the Google Maps API key.
// IMPORTANT: For this application to work with the map, you need a valid Google Maps API key.
// This key should be obtained from the Google Cloud Console.
// Ensure the "Maps JavaScript API" and "Places API" are enabled for your project and billing is set up.
const GOOGLE_MAPS_API_KEY = "AIzaSyD8Kw-ExFiY-7RRRDyzZexLFI7thTefpeU"; // Your updated API key

// Main App component for the SHA512 Coordinate Hash Finder
const App = () => {
    // State variables for input coordinates for each corner (manual entry/map populated)
    const [nwLat, setNwLat] = useState('');
    const [nwLon, setNwLon] = useState('');
    const [neLat, setNeLat] = useState('');
    const [neLon, setNeLon] = useState('');
    const [seLat, setSeLat] = useState('');
    const [seLon, setSeLon] = useState('');
    const [swLat, setSwLat] = useState('');
    const [swLon, setSwLon] = useState('');
    const [targetHash, setTargetHash] = useState('');

    // New state for precision
    const [precision, setPrecision] = useState(0); // 0 for integer, 1 for 1 decimal, etc.

    // State variables for application status and results
    const [status, setStatus] = useState('');
    const [foundCoordinate, setFoundCoordinate] = useState(null);
    const [isLoading, setIsLoading] = useState(false); // Overall loading, includes setup and scanning
    const [error, setError] = useState('');
    const [totalCoordinatesToScan, setTotalCoordinatesToScan] = useState(0);
    const [scannedCount, setScannedCount] = useState(0); // Added state for scanned coordinates count
    const [scannedCoordinatesList, setScannedCoordinatesList] = useState([]); // New state for the list of scanned coordinates

    // New state for mode switching and hash generation
    const [appMode, setAppMode] = useState('finder'); // 'finder' or 'generator'
    const [generatedHashes, setGeneratedHashes] = useState([]); // Store generated coordinate hashes

    // State for map-related functionalities
    const mapRef = useRef(null); // Ref to the map DOM element
    const googleMapRef = useRef(null); // Ref to the Google Map instance
    const markersRef = useRef([]); // Ref to store Google Maps marker instances (for selected corners)
    const searchInputRef = useRef(null); // Ref to the search input field
    const searchMarkerRef = useRef(null); // Ref to store the marker for search results
    const [selectedPoints, setSelectedPoints] = useState([]); // Array to store LatLng objects from map clicks
    const selectedPointsCurrentRef = useRef(selectedPoints); // Ref to hold the current value of selectedPoints state
    const [mapLoadError, setMapLoadError] = useState(''); // State for map loading errors
    const [searchMessage, setSearchMessage] = useState(''); // State for search-related messages
    const [isCryptoJSLoaded, setIsCryptoJSLoaded] = useState(false); // New state to track CryptoJS loading

    // New state for managing the scanning process
    const [isScanning, setIsScanning] = useState(false); // True when the hash comparison loop is active
    const stopScanFlagRef = useRef(false); // A ref to immediately signal the scan loop to stop
    const scanParamsRef = useRef(null); // Ref to store all necessary scan parameters across chunks

    // Labels for guiding corner selection
    const nextCornerLabels = ['North-West', 'North-East', 'South-East', 'South-West'];

    // Threshold for displaying the full list of coordinates
    const MAX_COORDINATES_FOR_LIST_DISPLAY = 200000;

    /**
     * Effect hook to keep the selectedPointsCurrentRef updated with the latest selectedPoints state.
     * This ensures the map click listener always accesses the most current state.
     */
    useEffect(() => {
        selectedPointsCurrentRef.current = selectedPoints;
    }, [selectedPoints]);

    /**
     * Effect hook to load the Google Maps API script and CryptoJS script dynamically.
     * Runs once when the component mounts.
     */
    useEffect(() => {
        // Load CryptoJS if not already loaded
        if (!window.CryptoJS) {
            const cryptoScript = document.createElement('script');
            cryptoScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js';
            cryptoScript.async = true;
            cryptoScript.defer = true;
            cryptoScript.onload = () => {
                setIsCryptoJSLoaded(true);
                console.log("CryptoJS loaded successfully.");
            };
            cryptoScript.onerror = () => {
                console.error("Failed to load CryptoJS.");
                setError("Failed to load cryptographic library. Please check your network connection.");
            };
            document.head.appendChild(cryptoScript);
        } else {
            setIsCryptoJSLoaded(true);
        }

        // Load Google Maps script if not already loaded
        if (window.google) {
            initMap();
            return;
        }

        const mapScript = document.createElement('script');
        // Add 'places' library to the script URL for the search functionality
        mapScript.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places&callback=initMap`;
        mapScript.async = true;
        mapScript.defer = true;
        document.head.appendChild(mapScript);

        // Define the global callback function for the Google Maps API
        window.initMap = () => {
            initMap();
        };

        mapScript.onerror = () => {
            setMapLoadError('Failed to load Google Maps. This is likely due to the "Maps JavaScript API" and/or "Places API" not being enabled in your Google Cloud project, an invalid API key, or billing not being enabled. Please check Google Cloud Console.');
        };

        // Cleanup function to remove the scripts and global callback when component unmounts
        return () => {
            document.head.removeChild(mapScript);
            // No need to remove cryptoScript explicitly as it's a global CDN load,
            // and trying to remove it might cause issues if other components rely on it.
            delete window.initMap;
        };
    }, []); // Empty dependency array to run once

    /**
     * Effect hook to initialize the map once the script is loaded and mapRef is available.
     * Also adds the click listener to the map.
     */
    useEffect(() => {
        if (window.google && mapRef.current && !googleMapRef.current) {
            initMap();
        }
    }, [mapRef.current]); // Re-run if mapRef.current changes

    /**
     * Initializes the Google Map instance and sets up the click listener and Places Autocomplete.
     */
    const initMap = () => {
        if (!mapRef.current || googleMapRef.current) return; // Prevent re-initialization

        try {
            const map = new window.google.maps.Map(mapRef.current, {
                center: { lat: 0, lng: 0 }, // Default center
                zoom: 2, // Default zoom level
                mapTypeId: 'satellite', // Changed to satellite view
                mapTypeControl: true, // Enabled map type control for switching views
                fullscreenControl: false, // Hide fullscreen control
                streetViewControl: false, // Hide street view control
            });

            googleMapRef.current = map; // Store the map instance

            // Initialize Places Autocomplete
            if (searchInputRef.current && window.google.maps.places) {
                const autocomplete = new window.google.maps.places.Autocomplete(searchInputRef.current);
                autocomplete.bindTo('bounds', map); // Bias results towards the current map viewport

                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();
                    setSearchMessage(''); // Clear previous search messages

                    if (!place.geometry || !place.geometry.location) {
                        setSearchMessage("No details available for input: '" + place.name + "'");
                        return;
                    }

                    // Clear previous search marker if exists
                    if (searchMarkerRef.current) {
                        searchMarkerRef.current.setMap(null);
                    }

                    // If the place has a geometry, then present it on a map.
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17); // A good zoom level for a specific place
                    }

                    // Add a marker for the searched place
                    searchMarkerRef.current = new window.google.maps.Marker({
                        map: map,
                        position: place.geometry.location,
                        title: place.name,
                        icon: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png' // Different color for search marker
                    });
                });
            } else {
                console.warn("Places library not loaded or search input ref not available.");
            }


            // Add a click listener to the map for selecting corner points or generating hashes
            map.addListener('click', (e) => {
                setSearchMessage(''); // Clear any search messages on map click
                handleMapClick(e);
            });
            if (appMode === 'generator') {
                setStatus('Hash Generator Mode: Click on the map to generate SHA512 hashes for coordinates.');
            } else {
                setStatus('Click on the map to select 4 corner points or use the search bar.');
            }
        } catch (err) {
            // More specific error message for map initialization issues
            setMapLoadError('Error initializing map. This could be due to the "Maps JavaScript API" and/or "Places API" not being enabled in your Google Cloud project, an invalid API key, or billing not being enabled. Please check Google Cloud Console. Error details: ' + err.message);
            console.error('Error initializing map:', err);
        }
    };

    /**
     * Handles map clicks based on the current app mode
     * @param {Object} e - The map click event
     */
    const handleMapClick = (e) => {
        if (appMode === 'generator') {
            handleGeneratorMapClick(e);
        } else {
            handleFinderMapClick(e);
        }
    };

    /**
     * Handles map clicks in finder mode (original functionality)
     * @param {Object} e - The map click event
     */
    const handleFinderMapClick = (e) => {
        // Use the ref to get the current state value of selectedPoints
        const currentSelectedPoints = selectedPointsCurrentRef.current;

        if (currentSelectedPoints.length < 4) {
            const newPoint = { lat: e.latLng.lat(), lng: e.latLng.lng() };
            const updatedPoints = [...currentSelectedPoints, newPoint];
            setSelectedPoints(updatedPoints); // Update state, which will update selectedPointsCurrentRef via useEffect
            addMarker(newPoint); // Add a marker for the clicked point
            updateCornerInputs(updatedPoints); // Update the corner input fields

            if (updatedPoints.length < 4) { // If not all 4 points are selected yet
                setStatus(`Selected ${updatedPoints.length} of 4 corners. Click the ${nextCornerLabels[updatedPoints.length]} corner.`);
            } else { // All 4 points selected
                setStatus('Four points selected. You can now start the hash comparison.');
            }
        } else {
            setStatus('Already selected 4 corners. Click "Clear Map Points" to reset.');
        }
    };

    /**
     * Handles map clicks in generator mode
     * @param {Object} e - The map click event
     */
    const handleGeneratorMapClick = (e) => {
        if (!isCryptoJSLoaded) {
            setError("Cryptographic library is not loaded yet. Please wait a moment and try again.");
            return;
        }

        const lat = e.latLng.lat();
        const lng = e.latLng.lng();

        // Format coordinate string with current precision
        const coordinateString = `${lat.toFixed(precision)},${lng.toFixed(precision)}`;

        // Generate SHA512 hash
        const hash = CryptoJS.SHA512(coordinateString).toString(CryptoJS.enc.Hex);

        // Create new hash entry
        const newHashEntry = {
            coordinate: coordinateString,
            lat: lat.toFixed(precision),
            lng: lng.toFixed(precision),
            hash: hash,
            timestamp: new Date().toLocaleTimeString()
        };

        // Add to generated hashes list
        setGeneratedHashes(prev => [newHashEntry, ...prev]);

        // Add marker to map
        addGeneratorMarker({ lat, lng }, coordinateString, hash);

        setStatus(`Generated hash for coordinate: ${coordinateString}`);
        setError(''); // Clear any previous errors
    };

    /**
     * Adds a marker to the map for a given LatLng point (for selected corners).
     * @param {Object} point - The {lat, lng} object for the marker.
     */
    const addMarker = (point) => {
        if (!googleMapRef.current) return;
        const marker = new window.google.maps.Marker({
            position: point,
            map: googleMapRef.current,
            title: `Lat: ${point.lat.toFixed(4)}, Lng: ${point.lng.toFixed(4)}`,
            icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png' // Blue dot for selected corners
        });
        markersRef.current.push(marker); // Store the marker instance
    };

    /**
     * Adds a marker to the map for generated hash coordinates
     * @param {Object} point - The {lat, lng} object for the marker
     * @param {string} coordinateString - The formatted coordinate string
     * @param {string} hash - The generated hash
     */
    const addGeneratorMarker = (point, coordinateString, hash) => {
        if (!googleMapRef.current) return;
        const marker = new window.google.maps.Marker({
            position: point,
            map: googleMapRef.current,
            title: `${coordinateString}\nSHA512: ${hash.substring(0, 16)}...`,
            icon: 'http://maps.google.com/mapfiles/ms/icons/red-dot.png' // Red dot for generated hashes
        });
        markersRef.current.push(marker); // Store the marker instance
    };

    /**
     * Clears all markers from the map (both corner and search markers) and resets selected points.
     */
    const clearMapPoints = () => {
        // Remove all corner markers
        markersRef.current.forEach(marker => marker.setMap(null));
        markersRef.current = []; // Clear corner marker references

        // Remove search marker if it exists
        if (searchMarkerRef.current) {
            searchMarkerRef.current.setMap(null);
            searchMarkerRef.current = null;
        }

        setSelectedPoints([]); // Clear selected points state
        setGeneratedHashes([]); // Clear generated hashes

        // Clear the input fields
        setNwLat(''); setNwLon('');
        setNeLat(''); setNeLon('');
        setSeLat(''); setSeLon('');
        setSwLat(''); setSwLon('');

        // Set appropriate status message based on mode
        if (appMode === 'generator') {
            setStatus('Map points cleared. Click on the map to generate SHA512 hashes for coordinates.');
        } else {
            setStatus('Map points cleared. Click on the map to select 4 new corner points or use the search bar.');
        }

        setError(''); // Clear any related errors
        setFoundCoordinate(null);
        setTotalCoordinatesToScan(0);
        setScannedCount(0); // Reset scanned count
        setScannedCoordinatesList([]); // Clear the list
        setSearchMessage(''); // Clear any search messages
        if (searchInputRef.current) {
            searchInputRef.current.value = ''; // Clear search bar text
        }
        stopScanFlagRef.current = true; // Ensure any ongoing scan is stopped
        setIsLoading(false);
        setIsScanning(false);
    };

    /**
     * Updates the corner input fields based on the selected map points.
     * This ensures manual inputs reflect the map selections.
     * @param {Array} points - Array of selected {lat, lng} points.
     */
    const updateCornerInputs = (points) => {
        // Define an array of setter pairs for each corner
        const cornerSetters = [
            { setLat: setNwLat, setLon: setNwLon },
            { setLat: setNeLat, setLon: setNeLon },
            { setLat: setSeLat, setLon: setSeLon },
            { setLat: setSwLat, setLon: setSwLon },
        ];

        // Clear all inputs first to ensure accurate representation
        cornerSetters.forEach(setter => {
            setter.setLat('');
            setter.setLon('');
        });

        // Populate inputs based on selected points order
        points.forEach((point, index) => {
            if (cornerSetters[index]) {
                // Use toFixed(6) for display, but parseFloat will handle actual value
                cornerSetters[index].setLat(point.lat.toFixed(6));
                cornerSetters[index].setLon(point.lng.toFixed(6));
            }
        });
    };

    /**
     * The main scanning loop, designed to be called repeatedly via requestAnimationFrame
     * to prevent UI freezing.
     */
    const scanLoop = () => {
        // Check if the user has requested to stop the scan
        if (stopScanFlagRef.current) {
            setIsScanning(false);
            setIsLoading(false);
            setStatus('Scan stopped by user.');
            return;
        }

        const params = scanParamsRef.current;
        if (!params) { // Should not happen if initiated correctly
            setIsScanning(false);
            setIsLoading(false);
            setStatus('Scan parameters not initialized.');
            return;
        }

        let { currentStep, startLatScaled, endLatScaled, startLonScaled, endLonScaled, multiplier, precision, targetHash, totalCoords, shouldDisplayFullList } = params;
        let hashesProcessedInChunk = 0;
        const CHUNK_SIZE = 500; // Reduced chunk size for more frequent UI updates

        let foundMatchInChunk = false;
        let updatedList = []; // To store updates for the list if shouldDisplayFullList is true

        // Iterate through a chunk of coordinates
        for (let k = 0; k < CHUNK_SIZE && currentStep < totalCoords; k++) {
            // Calculate current scaled latitude and longitude from the linear currentStep
            const iLat = startLatScaled + Math.floor(currentStep / (endLonScaled - startLonScaled + 1));
            const iLon = startLonScaled + (currentStep % (endLonScaled - startLonScaled + 1));

            // Convert scaled integers back to actual lat/lon with precision
            const lat = iLat / multiplier;
            const lon = iLon / multiplier;

            // Format the coordinate string to the specified precision for hashing
            const coordinateString = `${lat.toFixed(precision)},${lon.toFixed(precision)}`;
            const hash = CryptoJS.SHA512(coordinateString).toString(CryptoJS.enc.Hex);
            hashesProcessedInChunk++;
            currentStep++; // Increment the global step counter

            // Log the exact string being hashed and its result for debugging
            console.log(`Hashing: "${coordinateString}" -> ${hash}`);
            console.log(`Comparing: Generated Hash "${hash.toLowerCase()}" === Target Hash "${targetHash}"`); // Added comparison log


            if (shouldDisplayFullList) {
                // Prepare update for the list item
                updatedList.push({
                    index: currentStep - 1, // Index of the item just processed
                    coordinate: coordinateString, // Store the exact string used for hashing
                    hash: hash,
                    status: (hash.toLowerCase() === targetHash) ? 'match' : 'scanned' // Compare in lowercase
                });
            }

            if (hash.toLowerCase() === targetHash) { // Compare in lowercase
                console.log(`MATCH DETECTED! Coordinate: ${coordinateString}, Hash: ${hash}`); // Log match
                setFoundCoordinate({ lat: lat.toFixed(precision), lon: lon.toFixed(precision) }); // Store found coords with precision
                foundMatchInChunk = true;
                break; // Found match, break inner chunk loop
            }
        }

        // Update the scanned count in the UI
        setScannedCount(prevCount => prevCount + hashesProcessedInChunk);
        params.currentStep = currentStep; // Update current step in ref for next chunk

        // Apply updates to the displayed list if applicable
        if (shouldDisplayFullList && updatedList.length > 0) {
            setScannedCoordinatesList(prevList => {
                const newList = [...prevList];
                updatedList.forEach(update => {
                    if (newList[update.index]) {
                        newList[update.index] = {
                            ...newList[update.index],
                            hash: update.hash,
                            status: update.status
                        };
                    }
                });
                return newList;
            });
        }

        if (foundMatchInChunk) {
            setIsScanning(false);
            setIsLoading(false);
            setStatus('Match found!');
            stopScanFlagRef.current = true; // Ensure scan stops immediately
            params.matchFound = true; // Mark that a match was found
        } else if (currentStep >= totalCoords) {
            // All coordinates scanned and no match found
            setIsScanning(false);
            setIsLoading(false);
            setStatus(`Scan complete: No match found within the specified area after checking ${totalCoords.toLocaleString()} coordinates.`);
        } else {
            // Continue scanning in the next animation frame to keep UI responsive
            requestAnimationFrame(scanLoop);
        }
    };

    /**
     * Handles the click event for the "Start Comparison" button.
     * Validates inputs, initializes the hashing process, and updates the UI.
     */
    const handleCompare = async () => {
        setError(''); // Clear previous errors
        setFoundCoordinate(null); // Clear previous results
        setStatus(''); // Clear previous status
        setTotalCoordinatesToScan(0); // Reset total coordinates
        setSearchMessage(''); // Clear any search messages
        setScannedCount(0); // Reset scanned count
        setScannedCoordinatesList([]); // Clear the list for a new scan
        stopScanFlagRef.current = false; // Reset stop flag for a new scan

        // Ensure CryptoJS is loaded before proceeding
        if (!isCryptoJSLoaded) {
            setError("Cryptographic library is not loaded yet. Please wait a moment and try again.");
            return;
        }

        // Use the values from the state (which can be populated by map clicks or manual entry)
        const coords = {
            nwLat: parseFloat(nwLat),
            nwLon: parseFloat(nwLon),
            neLat: parseFloat(neLat),
            neLon: parseFloat(neLon),
            seLat: parseFloat(seLat),
            seLon: parseFloat(seLon),
            swLat: parseFloat(swLat),
            swLon: parseFloat(swLon),
        };

        // Validate all input fields are filled
        const allCoordsFilled = Object.values(coords).every(val => !isNaN(val));
        if (!allCoordsFilled || !targetHash) {
            setError('Please ensure all four corner coordinates and the target hash are filled.');
            return;
        }

        // Validate latitude ranges for all corners
        const allLats = [coords.nwLat, coords.neLat, coords.seLat, coords.swLat];
        if (allLats.some(lat => lat < -90 || lat > 90)) {
            setError('All Latitudes must be between -90 and 90.');
            return;
        }
        // Validate longitude ranges for all corners
        const allLons = [coords.nwLon, coords.neLon, coords.seLon, coords.swLon];
        if (allLons.some(lon => lon < -180 || lon > 180)) {
            setError('All Longitudes must be between -180 and 180.');
            return;
        }

        // Determine the overall min/max latitude and longitude from the four corners
        const minLat = Math.min(...allLats);
        const maxLat = Math.max(...allLats);
        const minLon = Math.min(...allLons);
        const maxLon = Math.max(...allLons);

        // Calculate multiplier based on precision
        const multiplier = Math.pow(10, precision);

        // Calculate the scaled integer boundaries for iteration
        const startLatScaled = Math.ceil(minLat * multiplier);
        const endLatScaled = Math.floor(maxLat * multiplier);
        const startLonScaled = Math.ceil(minLon * multiplier);
        const endLonScaled = Math.floor(maxLon * multiplier);

        // Calculate the number of coordinates to scan at the given precision
        const numLats = (endLatScaled >= startLatScaled) ? (endLatScaled - startLatScaled + 1) : 0;
        const numLons = (endLonScaled >= startLonScaled) ? (endLonScaled - startLonScaled + 1) : 0;

        const calculatedTotalCoords = numLats * numLons;
        setTotalCoordinatesToScan(calculatedTotalCoords);

        // Determine if we should generate and display the full list
        const shouldDisplayFullList = calculatedTotalCoords <= MAX_COORDINATES_FOR_LIST_DISPLAY;

        // --- Console Logging for Debugging ---
        console.log("--- Scan Initialization Details ---");
        console.log(`Input minLat: ${minLat}, maxLat: ${maxLat}`);
        console.log(`Input minLon: ${minLon}, maxLon: ${maxLon}`);
        console.log(`Precision: ${precision}, Multiplier: ${multiplier}`);
        console.log(`Scaled Lat Range: ${startLatScaled} to ${endLatScaled} (Count: ${numLats})`);
        console.log(`Scaled Lon Range: ${startLonScaled} to ${endLonScaled} (Count: ${numLons})`);
        console.log(`Calculated Total Coordinates to Scan: ${calculatedTotalCoords}`);
        console.log(`Should Display Full List: ${shouldDisplayFullList}`);
        console.log(`Target Hash (raw): "${targetHash}"`); // Added console log for raw target hash
        console.log(`Target Hash Length (raw): ${targetHash.length}`); // Added console log for raw target hash length
        console.log("-----------------------------------");
        // --- End Console Logging ---


        // Validate target hash format (SHA512 is 128 hex characters)
        // Trim whitespace before validation
        const trimmedTargetHash = targetHash.trim();
        if (!/^[0-9a-fA-F]{128}$/.test(trimmedTargetHash)) {
            setError('Target SHA512 hash must be 128 hexadecimal characters. Please check for extra spaces or hidden characters.');
            return;
        }

        // If no coordinates are found at the specified precision
        if (calculatedTotalCoords === 0) {
            setError('No coordinates found within the specified area at the given precision. Please adjust your corner coordinates or precision setting.');
            return;
        }

        // Warn about very large scans
        if (calculatedTotalCoords > 100000000) { // Increased warning threshold for very large scans
            setError(`Warning: This scan will check approximately ${calculatedTotalCoords.toLocaleString()} coordinates. This is an extremely large number and may take a very long time, potentially freezing your browser. Consider reducing the area or precision significantly.`);
            // You might want to add a confirmation dialog here before proceeding with such a large scan
            // For now, it just shows an error and allows the user to click "Start Comparison" again.
        }

        setIsLoading(true); // Start overall loading state
        setIsScanning(true); // Indicate that scanning is active

        if (shouldDisplayFullList) {
            const initialList = [];
            const currentPrecision = precision; // Capture current precision value
            for (let iLat = startLatScaled; iLat <= endLatScaled; iLat++) {
                const lat = iLat / multiplier;
                for (let iLon = startLonScaled; iLon <= endLonScaled; iLon++) {
                    const lon = iLon / multiplier;
                    initialList.push({
                        coordinate: `${lat.toFixed(currentPrecision)},${lon.toFixed(currentPrecision)}`,
                        hash: '', // Will be filled during scan
                        status: 'pending' // Initial status
                    });
                }
            }
            setScannedCoordinatesList(initialList);
            setStatus(`Preparing to scan ${calculatedTotalCoords.toLocaleString()} coordinates...`);
        } else {
            // For very large scans, just show progress, not the full list
            setError(`Warning: The total number of coordinates (${calculatedTotalCoords.toLocaleString()}) is too large to display in a list. Only scan progress will be shown. Consider reducing the area or precision.`);
            setStatus(`Starting scan for ${calculatedTotalCoords.toLocaleString()} coordinates...`);
        }

        // Store all necessary parameters for the scan loop in a ref
        scanParamsRef.current = {
            startLatScaled, endLatScaled, startLonScaled, endLonScaled,
            multiplier, precision,
            targetHash: trimmedTargetHash.toLowerCase(), // Store the trimmed and lowercased target hash
            totalCoords: calculatedTotalCoords,
            currentStep: 0, // Start from the first coordinate
            matchFound: false,
            shouldDisplayFullList // Pass this flag to scanLoop
        };

        // Start the recursive scanning process after a small delay to allow UI update
        requestAnimationFrame(scanLoop);
    };

    // Function to stop the scan
    const stopScan = () => {
        stopScanFlagRef.current = true; // Signal the scan loop to stop
        setIsLoading(false); // Update loading state immediately
        setIsScanning(false);
        setStatus('Scan stopped by user.');
    };

    // Function to switch between modes
    const switchMode = (newMode) => {
        setAppMode(newMode);
        clearMapPoints(); // Clear all existing points when switching modes
        setError(''); // Clear any errors

        if (newMode === 'generator') {
            setStatus('Hash Generator Mode: Click on the map to generate SHA512 hashes for coordinates.');
        } else {
            setStatus('Hash Finder Mode: Click on the map to select 4 corner points or use the search bar.');
        }
    };

    // Function to copy hash to clipboard
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setStatus('Hash copied to clipboard!');
            setTimeout(() => {
                if (appMode === 'generator') {
                    setStatus('Hash Generator Mode: Click on the map to generate SHA512 hashes for coordinates.');
                }
            }, 2000);
        }).catch(() => {
            setStatus('Failed to copy to clipboard');
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4 font-sans">
            <div className="bg-white p-8 rounded-xl shadow-2xl w-full max-w-2xl"> {/* Increased max-width */}
                <h1 className="text-3xl font-extrabold text-center text-gray-800 mb-6">
                    SHA512 Coordinate Hash {appMode === 'finder' ? 'Finder' : 'Generator'}
                </h1>

                {/* Mode Switcher */}
                <div className="mb-6">
                    <div className="flex justify-center space-x-4">
                        <button
                            onClick={() => switchMode('finder')}
                            className={`px-6 py-2 rounded-md font-semibold transition duration-200 ${
                                appMode === 'finder'
                                    ? 'bg-blue-600 text-white shadow-md'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                        >
                            Hash Finder
                        </button>
                        <button
                            onClick={() => switchMode('generator')}
                            className={`px-6 py-2 rounded-md font-semibold transition duration-200 ${
                                appMode === 'generator'
                                    ? 'bg-green-600 text-white shadow-md'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                        >
                            Hash Generator
                        </button>
                    </div>
                    <p className="text-center text-sm text-gray-600 mt-2">
                        {appMode === 'finder'
                            ? 'Find coordinates that match a specific SHA512 hash'
                            : 'Generate SHA512 hashes for clicked coordinates'}
                    </p>
                </div>

                {/* Map Section */}
                <div className="mb-6">
                    <h2 className="text-xl font-bold text-gray-700 mb-3">Select Area on Map</h2>
                    {mapLoadError && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md relative mb-4" role="alert">
                            <strong className="font-bold">Map Error!</strong>
                            <span className="block sm:inline ml-2">{mapLoadError}</span>
                        </div>
                    )}
                    <div className="relative mb-4">
                        <input
                            ref={searchInputRef}
                            type="text"
                            placeholder="Search for a place..."
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200 pr-10"
                        />
                        <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="20" height="20">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    {searchMessage && (
                        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-md relative mb-4" role="alert">
                            <strong className="font-bold">Info:</strong>
                            <span className="block sm:inline ml-2">{searchMessage}</span>
                        </div>
                    )}

                    <div
                        ref={mapRef}
                        className="w-full h-80 bg-gray-200 rounded-md shadow-inner mb-4" // Fixed height for map
                        style={{ minHeight: '300px' }} // Ensure a minimum height
                    >
                        {/* Map will be rendered here */}
                    </div>
                    <button
                        onClick={clearMapPoints}
                        className="w-full py-2 px-4 bg-red-500 text-white rounded-md font-semibold hover:bg-red-600 transition duration-200 ease-in-out transform hover:scale-105 active:scale-95 shadow-md"
                    >
                        Clear Map Points
                    </button>
                    <p className="text-gray-600 text-sm mt-2 text-center">{status}</p>
                </div>

                {/* Precision Setting */}
                <div className="mb-6">
                    <label htmlFor="precision" className="block text-sm font-medium text-gray-700 mb-1">
                        {appMode === 'finder' ? 'Scan Precision (Decimal Places)' : 'Coordinate Precision (Decimal Places)'}
                    </label>
                    <input
                        type="number"
                        id="precision"
                        value={precision}
                        onChange={(e) => setPrecision(Math.max(0, parseInt(e.target.value) || 0))} // Ensure non-negative integer
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                        placeholder="e.g., 0 for integer, 1 for 0.X, 2 for 0.XX"
                        min="0"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                        {appMode === 'finder'
                            ? 'Higher precision drastically increases scan time (e.g., 1 decimal place = 100x more coordinates).'
                            : 'Controls the number of decimal places in generated coordinate strings for hashing.'}
                    </p>
                </div>

                {/* Manual Input Section - Only show in finder mode */}
                {appMode === 'finder' && (
                    <>
                        <h2 className="text-xl font-bold text-gray-700 mb-3">Or Enter Coordinates Manually</h2>
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label htmlFor="nwLat" className="block text-sm font-medium text-gray-700 mb-1">NW Latitude</label>
                        <input type="number" id="nwLat" value={nwLat} onChange={(e) => setNwLat(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-90 to 90" step="any" />
                    </div>
                    <div>
                        <label htmlFor="nwLon" className="block text-sm font-medium text-gray-700 mb-1">NW Longitude</label>
                        <input type="number" id="nwLon" value={nwLon} onChange={(e) => setNwLon(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-180 to 180" step="any" />
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label htmlFor="neLat" className="block text-sm font-medium text-gray-700 mb-1">NE Latitude</label>
                        <input type="number" id="neLat" value={neLat} onChange={(e) => setNeLat(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-90 to 90" step="any" />
                    </div>
                    <div>
                        <label htmlFor="neLon" className="block text-sm font-medium text-gray-700 mb-1">NE Longitude</label>
                        <input type="number" id="neLon" value={neLon} onChange={(e) => setNeLon(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-180 to 180" step="any" />
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label htmlFor="seLat" className="block text-sm font-medium text-gray-700 mb-1">SE Latitude</label>
                        <input type="number" id="seLat" value={seLat} onChange={(e) => setSeLat(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-90 to 90" step="any" />
                    </div>
                    <div>
                        <label htmlFor="seLon" className="block text-sm font-medium text-gray-700 mb-1">SE Longitude</label>
                        <input type="number" id="seLon" value={seLon} onChange={(e) => setSeLon(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-180 to 180" step="any" />
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-6">
                    <div>
                        <label htmlFor="swLat" className="block text-sm font-medium text-gray-700 mb-1">SW Latitude</label>
                        <input type="number" id="swLat" value={swLat} onChange={(e) => setSwLat(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-90 to 90" step="any" />
                    </div>
                    <div>
                        <label htmlFor="swLon" className="block text-sm font-medium text-gray-700 mb-1">SW Longitude</label>
                        <input type="number" id="swLon" value={swLon} onChange={(e) => setSwLon(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200" placeholder="-180 to 180" step="any" />
                    </div>
                </div>

                        {/* Input field for target SHA512 hash */}
                        <div className="mb-6">
                            <label htmlFor="targetHash" className="block text-sm font-medium text-gray-700 mb-1">Target SHA512 Hash</label>
                            <input
                                type="text"
                                id="targetHash"
                                value={targetHash}
                                onChange={(e) => setTargetHash(e.target.value.trim())} // Trim whitespace here
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                                placeholder="Enter 128-character SHA512 hash"
                                maxLength="128"
                            />
                        </div>
                    </>
                )}

                {/* Error message display */}
                {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md relative mb-4" role="alert">
                        <strong className="font-bold">Error!</strong>
                        <span className="block sm:inline ml-2">{error}</span>
                    </div>
                )}

                {/* Action Buttons - Only show in finder mode */}
                {appMode === 'finder' && (
                    <div className="flex flex-col space-y-3">
                        <button
                            onClick={handleCompare}
                            disabled={isLoading || isScanning || !isCryptoJSLoaded} // Disable if loading, scanning, or crypto not loaded
                            className={`w-full py-3 px-4 rounded-md text-white font-semibold transition duration-300 ease-in-out transform ${
                                isLoading || isScanning || !isCryptoJSLoaded
                                    ? 'bg-gray-400 cursor-not-allowed'
                                    : 'bg-blue-600 hover:bg-blue-700 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl'
                            }`}
                        >
                            {isLoading && !isScanning ? 'Initializing...' : (isScanning ? 'Scanning...' : (isCryptoJSLoaded ? 'Start Comparison' : 'Loading Crypto Library...'))}
                        </button>

                        {isScanning && ( // Only show stop button when scanning is active
                            <button
                                onClick={stopScan}
                                className="w-full py-3 px-4 rounded-md text-white font-semibold bg-red-500 hover:bg-red-600 transition duration-300 ease-in-out transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                            >
                                Stop Scan
                            </button>
                        )}
                    </div>
                )}


                {/* Status and results display */}
                <div className="mt-6 text-center">
                    {totalCoordinatesToScan > 0 && !isScanning && !foundCoordinate && scannedCount === 0 && !error && (
                        <p className="text-gray-600 mb-2">Total coordinates to scan: {totalCoordinatesToScan.toLocaleString()}</p>
                    )}
                    {(isScanning || (totalCoordinatesToScan > 0 && scannedCount > 0)) && (
                        <p className="text-gray-600 mb-2">
                            Scanned: {scannedCount.toLocaleString()} / {totalCoordinatesToScan.toLocaleString()}
                        </p>
                    )}
                    {isLoading && (
                        <div className="flex items-center justify-center space-x-2">
                            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                            <p className="text-gray-600">{status}</p>
                        </div>
                    )}
                    {!isLoading && status && (
                        <p className="text-gray-700 font-medium">{status}</p>
                    )}
                    {foundCoordinate && (
                        <p className="text-green-600 text-lg font-bold mt-4">
                            Match found at: Latitude {foundCoordinate.lat}, Longitude {foundCoordinate.lon}
                        </p>
                    )}
                </div>

                {/* Hash Generator Results Display */}
                {appMode === 'generator' && (
                    <div className="mt-6">
                        <h2 className="text-xl font-bold text-gray-700 mb-3">Generated Hashes</h2>
                        {generatedHashes.length > 0 ? (
                            <div className="bg-gray-50 p-4 rounded-md h-96 overflow-y-auto border border-gray-200">
                                {generatedHashes.map((item, index) => (
                                    <div key={index} className="bg-white p-3 mb-3 rounded-md border border-gray-200 shadow-sm">
                                        <div className="flex items-center justify-between mb-2">
                                            <span className="font-mono text-sm text-gray-800 font-semibold">
                                                {item.coordinate}
                                            </span>
                                            <span className="text-xs text-gray-500">
                                                {item.timestamp}
                                            </span>
                                        </div>
                                        <div
                                            className="text-xs font-mono text-gray-600 break-all bg-gray-100 p-2 rounded cursor-pointer hover:bg-gray-200 transition duration-200"
                                            onClick={() => copyToClipboard(item.hash)}
                                            title="Click to copy hash to clipboard"
                                        >
                                            {item.hash}
                                        </div>
                                        <div className="mt-2 text-xs text-gray-500">
                                            Lat: {item.lat}, Lng: {item.lng}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="bg-gray-50 p-8 rounded-md text-center border border-gray-200">
                                <p className="text-gray-500 mb-2">No hashes generated yet</p>
                                <p className="text-sm text-gray-400">Click on the map to generate SHA512 hashes for coordinates</p>
                            </div>
                        )}
                    </div>
                )}

                {/* Scan Results List Display - Only show in finder mode */}
                {appMode === 'finder' && (
                    <div className="mt-6">
                    <h2 className="text-xl font-bold text-gray-700 mb-3">Scan Results List</h2>
                    {scannedCoordinatesList.length > 0 ? (
                        <div className="bg-gray-50 p-4 rounded-md h-96 overflow-y-auto border border-gray-200">
                            {scannedCoordinatesList.map((item, index) => (
                                <div key={index} className="flex items-center justify-between text-sm py-1 border-b border-gray-100 last:border-b-0">
                                    <span className="font-mono text-gray-800">{item.coordinate}</span>
                                    <span className="font-mono text-gray-600 truncate ml-2">{item.hash || 'Pending...'}</span>
                                    {item.status === 'match' && (
                                        <span className="text-green-500 ml-2 text-lg font-bold">💲</span>
                                    )}
                                    {item.status === 'scanned' && (
                                        <span className="text-red-500 ml-2 text-lg font-bold">✖</span>
                                    )}
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500 text-center">
                            {totalCoordinatesToScan > MAX_COORDINATES_FOR_LIST_DISPLAY
                                ? "List not displayed due to large number of coordinates. Progress above."
                                : "No scan initiated or no coordinates to display yet."}
                        </p>
                    )}
                    </div>
                )}
            </div>
        </div>
    );
};

// App component is now available globally
